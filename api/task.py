from fastapi import APIRouter, BackgroundTasks, HTTPException, Query
from datetime import datetime
from typing import Optional
from env import BASE_DIR
from utils.email_util import send_email
from utils.response import ResponseModel, success
from utils.mongodb import get_mongodb
from models.api_portal import ApiPortalQueryRequest, ApiPortalQueryResponse, ApiPortalPublishApi
import logging
router = APIRouter()
log = logging.getLogger(__name__)

@router.get("/send_email_to_bank_cn", response_model=ResponseModel)
async def send_email_to_bank_cn(background_tasks: BackgroundTasks):
    today = datetime.today().weekday()
    if today == 5 or today == 6:
        log.info(f'今天是周{today +1 },不需要发邮件')
        return success()
    """
    银行	邮箱号
    工商银行	<EMAIL>
    农业银行	"<EMAIL>
    <EMAIL>"
    民生银行	<EMAIL>
    中国银行	<EMAIL>
    招商银行	<EMAIL>
    中信银行	<EMAIL>
    中原银行	"<EMAIL>
    <EMAIL>"
    平安银行-郑州	<EMAIL>
    邮政银行	<EMAIL>
    """
    email_list = [
        ['<EMAIL>'],
        ['<EMAIL>', '<EMAIL>'],
        ['<EMAIL>'],
        ['<EMAIL>'],
        ['<EMAIL>'],
        ['<EMAIL>'],
        ['<EMAIL>', '<EMAIL>'],
        ['<EMAIL>'],
        ['<EMAIL>']
    ]

    # email_list = [
    #     ['<EMAIL>', '<EMAIL>'],
    #     ['<EMAIL>']
    # ]
    cc_list = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ]
    # cc_list = [
    #     '<EMAIL>'
    # ]
    attachment_path = str(BASE_DIR / 'files' / '每日银行邮箱推送定时任务-境内银行.xlsx')
    for bank_email in email_list:
        background_tasks.add_task(send_email, bank_email, cc_list, '每日价格更新',
                                  '您好，请帮忙按照以下表格模板，更新当日最新的优惠价格，回复时请标注银行名称，如表内信息不完整，请帮忙补充，谢谢！',
                                  attachment_path)
    return success()


@router.get("/send_email_to_bank_foreign", response_model=ResponseModel)
async def send_email_to_bank_foreign(background_tasks: BackgroundTasks):
    """
    银行	邮箱号
    中银香港	<EMAIL> ；
    招商永隆	"<EMAIL>；
    <EMAIL>；
    <EMAIL>"
    渣打银行	"<EMAIL>
    <EMAIL> <EMAIL>"
    渣打银行-印尼	"<EMAIL>
    <EMAIL>
    <EMAIL>
    <EMAIL>
    <EMAIL>
    <EMAIL>"
    UBS瑞银	"<EMAIL>
    <EMAIL>"
    汇丰银行-印尼	"<EMAIL>
    <EMAIL>
    <EMAIL>"
    汇丰银行-新加坡	"<EMAIL>
    <EMAIL>
    <EMAIL>
    <EMAIL>"
    花旗银行	 <EMAIL> <EMAIL>
    """

    today = datetime.today().weekday()
    if today == 5 or today == 6:
        log.info(f'今天是周{today +1 },不需要发邮件')
        return success()
    email_list = [
        ['<EMAIL>'],
        ['<EMAIL>','<EMAIL>','<EMAIL>'],
        ['<EMAIL>','<EMAIL>','<EMAIL>'],
        ['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>'],
        ['<EMAIL>','<EMAIL>'],
        ['<EMAIL>','<EMAIL>','<EMAIL>'],
        ['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>'],
        ['<EMAIL> <EMAIL>']
    ]
    cc_list = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ]
    attachment_path = str(BASE_DIR / 'files' / '每日银行邮箱推送定时任务-境外银行.xlsx')
    for bank_email in email_list:
        background_tasks.add_task(send_email, bank_email, cc_list, '每日价格更新',
                                  '您好，请帮忙按照以下表格模板，更新当日最新的优惠价格，回复时请标注银行名称，如表内信息不完整，请帮忙补充，谢谢！',
                                  attachment_path)
    return success()


@router.get("/api_portal_publish_apis", response_model=ResponseModel)
async def get_api_portal_publish_apis(
    start_time: str = Query(..., description="开始时间，格式：YYYY-MM-DD HH:MM:SS", example="2025-05-30 00:00:00"),
    end_time: str = Query(..., description="结束时间，格式：YYYY-MM-DD HH:MM:SS", example="2025-06-03 23:59:59"),
    limit: int = Query(1000, description="限制返回数量", ge=1, le=10000),
    skip: int = Query(0, description="跳过数量", ge=0)
):
    """
    查询API门户发布API数据

    根据发布时间范围查询P_ApiPortalPublishApis集合中的数据

    Args:
        start_time: 开始时间
        end_time: 结束时间
        limit: 限制返回数量，默认1000
        skip: 跳过数量，默认0

    Returns:
        查询结果列表
    """
    try:
        # 获取MongoDB连接
        mongodb = await get_mongodb()
        collection = mongodb.get_collection("P_ApiPortalPublishApis")

        # 构建查询条件
        query = {
            "$and": [
                {"publishTime": {"$gt": start_time}},
                {"publishTime": {"$lt": end_time}}
            ]
        }

        log.info(f"Querying MongoDB with: {query}, limit: {limit}, skip: {skip}")

        # 执行查询
        cursor = collection.find(query).sort("publishTime", -1).limit(limit).skip(skip)

        # 获取结果
        results = []
        async for document in cursor:
            # 将ObjectId转换为字符串
            if "_id" in document:
                document["_id"] = str(document["_id"])
            results.append(ApiPortalPublishApi(**document))

        # 获取总数 (使用老版本的count方法)
        total = await collection.count(query)

        log.info(f"Found {len(results)} documents, total: {total}")

        # 构建响应
        response_data = ApiPortalQueryResponse(
            total=total,
            data=results,
            limit=limit,
            skip=skip
        )

        return success(data=response_data.model_dump())

    except Exception as e:
        log.error(f"Error querying API portal data: {e}")
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")
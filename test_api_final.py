#!/usr/bin/env python3
"""
最终API测试脚本
"""
import requests
import json
from datetime import datetime

def test_api():
    """测试API接口"""
    base_url = "http://localhost:8086"
    endpoint = "/api/v1/task/api_portal_publish_apis"
    
    # 测试参数
    params = {
        "start_time": "2025-05-30 00:00:00",
        "end_time": "2025-06-03 23:59:59",
        "limit": 10,
        "skip": 0
    }
    
    print("🚀 测试MongoDB API接口...")
    print(f"📍 URL: {base_url}{endpoint}")
    print(f"📋 参数: {params}")
    print("-" * 50)
    
    try:
        # 发送请求
        response = requests.get(f"{base_url}{endpoint}", params=params)
        
        print(f"📊 状态码: {response.status_code}")
        print(f"📄 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 请求成功!")
            print(f"📈 响应数据:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # 解析数据
            if "data" in data:
                result_data = data["data"]
                print(f"\n📊 统计信息:")
                print(f"   总记录数: {result_data.get('total', 0)}")
                print(f"   返回记录数: {len(result_data.get('data', []))}")
                print(f"   限制数量: {result_data.get('limit', 0)}")
                print(f"   跳过数量: {result_data.get('skip', 0)}")
                
                # 显示第一条记录的详细信息
                records = result_data.get('data', [])
                if records:
                    print(f"\n📝 第一条记录详情:")
                    first_record = records[0]
                    for key, value in first_record.items():
                        print(f"   {key}: {value}")
            
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败: 请确保服务器正在运行在 http://localhost:8086")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_api()

# 生产环境配置

# 数据库配置
DATABASE_URL=mysql+pymysql://devops:wldev%402022@***********:3306/cloud_wecom
DATABASE_SCHEMA=cloud_wecom

# 服务配置
SERVER_PORT=8085
SERVER_HOST=************

# 静态文件目录
#STATIC_DIR=/data/wl-iam/static
STATIC_DIR=static
ESB_URL=http://***********:8080/restcloud/
ESB_USERNAME=feishu_user
ESB_PASSWORD=AFDA2E1FCB81BB05
ESB_APPKEY=67fca7fa054df711caea5275

DOMAIN = https://iback.weilongsoft.com

MAIL_USERNAME = <EMAIL>
MAIL_PASSWORD = iPJb3JHdVqrZlWem
MAIL_PORT = 587
MAIL_SERVER = smtp.feishu.cn

# MongoDB配置
# 格式: mongodb://用户名:密码@主机:端口/数据库名
MONGO_URL=mongodb://mongouser:Xiaofu1125!@***********:27017
MONGO_DATABASE=RestCloud_V45PUB_Gateway
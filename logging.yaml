# logging_config.yaml
version: 1
disable_existing_loggers: false # 保留 Uvicorn 等库的 logger

formatters:
  # Uvicorn 控制台默认格式器
  default:
    (): uvicorn.logging.DefaultFormatter
    fmt: "%(levelprefix)s %(message)s"
    use_colors: null # 自动检测

  # Uvicorn 控制台访问日志格式器
  access:
    (): uvicorn.logging.AccessFormatter
    fmt: '%(levelprefix)s %(client_addr)s - "%(request_line)s" %(status_code)s'

  # 文件日志使用的详细格式器 (包含进程ID)
  custom_verbose:
    format: "%(asctime)s - %(name)s - %(levelname)s - %(process)d - %(thread)d - %(message)s"
    datefmt: "%Y-%m-%d %H:%M:%S"

handlers:
  # --- 控制台 Handlers ---
  # Uvicorn 和 Root Logger 的默认控制台输出
  console:
    class: logging.StreamHandler
    formatter: default
    stream: ext://sys.stdout

  # Uvicorn 访问日志的专用控制台输出
  access_console:
    class: logging.StreamHandler
    formatter: access
    stream: ext://sys.stdout

  # --- 文件 Handlers ---
  # 主要的应用和 Uvicorn 日志文件 (使用标准轮转处理器)
  concurrent_file:
    class: logging.handlers.RotatingFileHandler
    formatter: custom_verbose         # 使用包含 PID 的详细格式
    filename: ./logs/app.log          # 所有进程写入这一个文件
    maxBytes: 10485760                # 10MB
    backupCount: 5                    # 保留5个备份文件
    encoding: utf8

  # 访问日志文件 (这里仍使用 WatchedFileHandler，如果你希望它也轮转且多进程安全，也可改为 ConcurrentRotatingFileHandler)
  access_file:
    class: logging.handlers.WatchedFileHandler # 或者 concurrent_log_handler.ConcurrentRotatingFileHandler
    formatter: custom_verbose         # 访问日志文件使用详细格式 (包含PID)
    filename: ./logs/access.log
    encoding: utf8
    # 如果改为 ConcurrentRotatingFileHandler，需要添加 maxBytes 和 backupCount

loggers:
  # Uvicorn 主 logger
  uvicorn:
    handlers:
      - console           # 输出到控制台 (默认格式)
      - concurrent_file   # 输出到 app.log (详细格式, 多进程安全)
    level: INFO
    propagate: false      # 不向 root 传递，避免重复记录

  # Uvicorn 错误 logger
  uvicorn.error:
    handlers:
      - console
      - concurrent_file
    level: INFO           # 或者根据需要设为 WARNING 或 ERROR
    propagate: false

  # Uvicorn 访问日志 logger
  uvicorn.access:
    handlers:
      - access_console    # 输出到控制台 (访问格式)
      - access_file       # 输出到 access.log (详细格式)
    level: INFO
    propagate: false      # 访问日志通常独立，不传递

  # 根 logger (捕获你应用程序代码中的日志)
  root:
    handlers:
      - console           # 应用日志也输出到控制台 (默认格式)
      - concurrent_file   # 应用日志也输出到 app.log (详细格式, 多进程安全)
    level: DEBUG          # 设置你希望的应用日志级别 (例如 DEBUG, INFO, WARNING)
    # propagate: true (默认即为 true，这里无需显式写出)
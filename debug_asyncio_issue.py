#!/usr/bin/env python3
"""
调试asyncio.coroutine问题的详细测试
"""
import asyncio
import sys
import os
import traceback

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入过程中是否出现错误"""
    print("=== 测试导入过程 ===")
    
    try:
        print("1. 导入asyncio...")
        import asyncio
        print(f"   ✓ asyncio版本: {asyncio.__version__ if hasattr(asyncio, '__version__') else 'N/A'}")
        print(f"   ✓ 可用属性: {[attr for attr in dir(asyncio) if 'coroutine' in attr.lower()]}")
        
        print("2. 导入motor...")
        import motor
        print(f"   ✓ Motor版本: {motor.version}")
        
        print("3. 导入motor.motor_asyncio...")
        from motor.motor_asyncio import AsyncIOMotorClient
        print("   ✓ AsyncIOMotorClient导入成功")
        
        print("4. 导入config...")
        from config import MONGO_URL, MONGO_DATABASE
        print(f"   ✓ MONGO_URL: {MONGO_URL}")
        print(f"   ✓ MONGO_DATABASE: {MONGO_DATABASE}")
        
        print("5. 导入utils.mongodb...")
        from utils.mongodb import MongoDB, mongodb, get_mongodb
        print("   ✓ MongoDB类导入成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 导入失败: {e}")
        print(f"   详细错误: {traceback.format_exc()}")
        return False

async def test_mongodb_operations():
    """测试MongoDB操作"""
    print("\n=== 测试MongoDB操作 ===")
    
    try:
        print("1. 创建MongoDB实例...")
        from utils.mongodb import MongoDB
        db = MongoDB()
        print("   ✓ MongoDB实例创建成功")
        
        print("2. 测试连接...")
        await db.connect()
        print("   ✓ 连接方法调用成功")
        
        print("3. 测试断开连接...")
        await db.disconnect()
        print("   ✓ 断开连接成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ MongoDB操作失败: {e}")
        print(f"   详细错误: {traceback.format_exc()}")
        return False

async def test_get_mongodb():
    """测试get_mongodb函数"""
    print("\n=== 测试get_mongodb函数 ===")
    
    try:
        from utils.mongodb import get_mongodb
        
        print("1. 调用get_mongodb()...")
        db_instance = await get_mongodb()
        print("   ✓ get_mongodb()调用成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ get_mongodb()失败: {e}")
        print(f"   详细错误: {traceback.format_exc()}")
        return False

def main():
    """主测试函数"""
    print("开始调试asyncio.coroutine问题...")
    print(f"Python版本: {sys.version}")
    
    # 测试导入
    import_success = test_imports()
    
    if not import_success:
        print("\n❌ 导入阶段失败，无法继续测试")
        return False
    
    # 测试异步操作
    try:
        print("\n开始异步测试...")
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # 测试MongoDB操作
        mongodb_success = loop.run_until_complete(test_mongodb_operations())
        
        # 测试get_mongodb
        get_mongodb_success = loop.run_until_complete(test_get_mongodb())
        
        loop.close()
        
        if mongodb_success and get_mongodb_success:
            print("\n✅ 所有测试通过！")
            return True
        else:
            print("\n❌ 部分测试失败")
            return False
            
    except Exception as e:
        print(f"\n❌ 异步测试失败: {e}")
        print(f"详细错误: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 调试完成：没有发现asyncio.coroutine问题")
    else:
        print("\n🔍 调试完成：发现了问题，请查看上面的错误信息")

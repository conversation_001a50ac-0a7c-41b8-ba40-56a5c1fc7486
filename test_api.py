#!/usr/bin/env python3
"""
测试MongoDB API接口
"""
import asyncio
import sys
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append('.')

from utils.mongodb import get_mongodb
from models.api_portal import ApiPortalPublishApi


async def test_mongodb_connection():
    """测试MongoDB连接"""
    try:
        print("正在测试MongoDB连接...")
        mongodb = await get_mongodb()
        collection = mongodb.get_collection("P_ApiPortalPublishApis")
        
        # 测试查询
        count = await collection.count_documents({})
        print(f"集合 P_ApiPortalPublishApis 中共有 {count} 条记录")
        
        # 测试查询条件
        query = {
            "$and": [
                {"publishTime": {"$gt": "2025-05-30 00:00:00"}},
                {"publishTime": {"$lt": "2025-06-03 23:59:59"}}
            ]
        }
        
        filtered_count = await collection.count_documents(query)
        print(f"符合时间条件的记录数: {filtered_count}")
        
        # 获取一些示例数据
        cursor = collection.find(query).limit(5)
        print("\n示例数据:")
        async for doc in cursor:
            print(f"- ID: {doc.get('_id')}, publishTime: {doc.get('publishTime')}")
            
        print("\nMongoDB连接测试成功!")
        
    except Exception as e:
        print(f"MongoDB连接测试失败: {e}")
        return False
    
    return True


async def test_api_model():
    """测试API数据模型"""
    try:
        print("\n正在测试API数据模型...")
        
        # 创建测试数据
        test_data = {
            "_id": "507f1f77bcf86cd799439011",
            "publishTime": "2025-06-01 10:30:00",
            "apiName": "测试API",
            "apiVersion": "v1.0",
            "apiDescription": "这是一个测试API",
            "publishStatus": "published",
            "publishUser": "admin"
        }
        
        # 测试模型创建
        api_model = ApiPortalPublishApi(**test_data)
        print(f"模型创建成功: {api_model.apiName}")
        print(f"模型JSON: {api_model.dict()}")
        
        print("API数据模型测试成功!")
        return True
        
    except Exception as e:
        print(f"API数据模型测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("开始测试MongoDB API接口...")
    print("=" * 50)
    
    # 测试数据模型
    model_test = await test_api_model()
    
    # 测试MongoDB连接
    db_test = await test_mongodb_connection()
    
    print("\n" + "=" * 50)
    if model_test and db_test:
        print("✅ 所有测试通过!")
        print("\n接口使用说明:")
        print("GET /api/v1/task/api_portal_publish_apis")
        print("参数:")
        print("  - start_time: 开始时间 (例: 2025-05-30 00:00:00)")
        print("  - end_time: 结束时间 (例: 2025-06-03 23:59:59)")
        print("  - limit: 限制数量 (默认: 1000)")
        print("  - skip: 跳过数量 (默认: 0)")
    else:
        print("❌ 部分测试失败，请检查配置")


if __name__ == "__main__":
    asyncio.run(main())

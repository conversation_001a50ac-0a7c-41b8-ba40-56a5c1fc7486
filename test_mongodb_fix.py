#!/usr/bin/env python3
"""
Test script to verify that the MongoDB connection works without AttributeError
"""
import asyncio
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_mongodb_connection():
    """Test MongoDB connection to verify the fix"""
    try:
        # Import the MongoDB utilities
        from utils.mongodb import get_mongodb, mongodb
        
        print("Testing MongoDB connection...")
        
        # Test getting MongoDB instance
        db_instance = await get_mongodb()
        print("✓ Successfully got MongoDB instance")
        
        # Test if client is connected
        if db_instance.client is not None:
            print("✓ MongoDB client is initialized")
        else:
            print("⚠ MongoDB client is None (connection may have failed)")
            
        # Test disconnection
        await mongodb.disconnect()
        print("✓ Successfully disconnected from MongoDB")
        
        print("\n🎉 All tests passed! The AttributeError has been fixed.")
        
    except AttributeError as e:
        if "asyncio" in str(e) and "coroutine" in str(e):
            print(f"❌ AttributeError still exists: {e}")
            return False
        else:
            print(f"❌ Different AttributeError: {e}")
            return False
    except Exception as e:
        print(f"⚠ Other error (not the asyncio.coroutine issue): {e}")
        print("This might be expected if MongoDB server is not running")
        return True  # The asyncio.coroutine error is fixed even if MongoDB server is not available
    
    return True

if __name__ == "__main__":
    result = asyncio.run(test_mongodb_connection())
    if result:
        print("\n✅ Fix successful: asyncio.coroutine AttributeError resolved")
    else:
        print("\n❌ Fix failed: asyncio.coroutine AttributeError still exists")

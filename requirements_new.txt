accelerate==1.6.0
aiocache==0.12.3
aiofiles==24.1.0
aiohappyeyeballs==2.4.8
aiohttp==3.11.11
aiosignal==1.3.2
aiosmtplib==3.0.2
alembic==1.14.0
altgraph==0.17.4
amqp==5.2.0
annotated-types==0.7.0
anthropic==0.49.0
anyio==4.8.0
APScheduler==3.10.4
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
asgiref==3.8.1
async-timeout==4.0.3
attrs==25.1.0
Authlib==1.4.1
av==14.2.0
azure-ai-documentintelligence==1.0.0
azure-core==1.32.0
azure-identity==1.20.0
azure-storage-blob==12.24.1
backoff==2.2.1
backports.tarfile==1.2.0
bcrypt==4.2.0
beautifulsoup4==4.13.3
bidict==0.23.1
billiard==4.2.0
bitarray==3.1.0
black==24.8.0
blinker==1.8.2
boto3==1.35.53
botocore==1.35.99
Brotli==1.1.0
build==1.2.2.post1
CacheControl==0.14.3
cachetools==5.5.2
celery==5.4.0
certifi==2025.1.31
cffi==1.16.0
chardet==5.2.0
charset-normalizer==3.4.1
chroma-hnswlib==0.7.6
chromadb==0.6.2
cleo==2.1.0
click==8.1.8
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
colbert-ai==0.2.21
colorama==0.4.6
colorclass==2.2.2
coloredlogs==15.0.1
compressed_rtf==1.0.6
concurrent-log-handler==0.9.25
cors==1.0.1
crashtest==0.4.1
cryptography==44.0.2
cssselect2==0.8.0
ctranslate2==4.5.0
dataclasses-json==0.6.7
datasets==3.3.2
defusedxml==0.7.1
dep-logic==0.5.0
Deprecated==1.2.18
dill==0.3.8
distlib==0.3.9
distro==1.9.0
dnspython==2.7.0
docker==7.1.0
docx2txt==0.8
duckduckgo_search==7.3.2
dulwich==0.22.8
durationpy==0.9
easygui==0.98.3
ebcdic==1.1.1
ecdsa==0.19.0
einops==0.8.0
email_validator==2.2.0
emoji==2.14.1
et_xmlfile==2.0.0
eval_type_backport==0.2.2
Events==0.5
exceptiongroup==1.2.2
extract-msg==0.53.1
fake-useragent==1.5.1
fastapi==0.115.7
fastapi-mail==1.4.2
faster-whisper==1.1.1
fastjsonschema==2.21.1
filelock==3.15.4
filetype==1.2.0
findpython==0.6.3
firecrawl-py==1.12.0
Flask==3.0.3
flatbuffers==25.2.10
fonttools==4.56.0
fpdf2==2.8.2
frozenlist==1.5.0
fsspec==2024.12.0
ftfy==6.2.3
future==1.0.0
gevent==24.2.1
git-python==1.0.3
gitdb==4.0.12
GitPython==3.1.44
google-ai-generativelanguage==0.6.6
google-api-core==2.24.1
google-api-python-client==2.162.0
google-auth==2.38.0
google-auth-httplib2==0.2.0
google-auth-oauthlib==1.2.1
google-cloud-core==2.4.2
google-cloud-storage==2.19.0
google-crc32c==1.6.0
google-generativeai==0.7.2
google-resumable-media==2.7.2
googleapis-common-protos==1.63.2
greenlet==3.1.1
grpcio==1.67.1
grpcio-status==1.63.0rc1
grpcio-tools==1.62.3
gunicorn==22.0.0
h11==0.14.0
h2==4.2.0
hishel==0.1.2
hpack==4.1.0
html5lib==1.1
httpcore==1.0.7
httplib2==0.22.0
httptools==0.6.4
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.29.1
humanfriendly==10.0
hyperframe==6.1.0
id==1.5.0
idna==3.7
importlib_metadata==7.1.0
importlib_resources==6.5.2
iniconfig==2.0.0
installer==0.7.0
isodate==0.7.2
itsdangerous==2.2.0
jaraco.classes==3.4.0
jaraco.context==6.0.1
jaraco.functools==4.1.0
Jinja2==3.1.4
jiter==0.8.2
jmespath==1.0.1
joblib==1.4.2
jsonpatch==1.33
jsonpointer==3.0.0
keyring==25.6.0
kombu==5.3.7
kubernetes==32.0.1
langchain==0.3.7
langchain-community==0.3.7
langchain-core==0.3.41
langchain-text-splitters==0.3.6
langdetect==1.0.9
langfuse==2.44.0
langsmith==0.1.147
lark==1.1.9
lark-oapi==1.4.13
ldap3==2.9.1
loguru==0.7.2
lxml==5.3.1
Mako==1.3.9
Markdown==3.7
markdown-it-py==3.0.0
MarkupSafe==2.1.5
marshmallow==3.26.1
mdurl==0.1.2
mmh3==5.1.0
monotonic==1.6
more-itertools==10.6.0
motor==2.3.1
mpmath==1.3.0
msal==1.31.1
msal-extensions==1.2.0
msgpack==1.1.0
msoffcrypto-tool==5.4.2
multidict==6.1.0
multiprocess==0.70.16
mypy-extensions==1.0.0
nest-asyncio==1.6.0
networkx==3.2.1
ninja==********
nltk==3.9.1
numpy==1.26.4
nv==0.0.7
oauthlib==3.2.2
olefile==0.47
oletools==0.60.2
onnxruntime==1.19.2
openai==1.65.3
opencv-python==*********
opencv-python-headless==*********
openpyxl==3.1.5
opensearch-py==2.8.0
opentelemetry-api==1.27.0
opentelemetry-exporter-otlp-proto-common==1.27.0
opentelemetry-exporter-otlp-proto-grpc==1.27.0
opentelemetry-instrumentation==0.48b0
opentelemetry-instrumentation-asgi==0.48b0
opentelemetry-instrumentation-fastapi==0.48b0
opentelemetry-proto==1.27.0
opentelemetry-sdk==1.27.0
opentelemetry-semantic-conventions==0.48b0
opentelemetry-util-http==0.48b0
orjson==3.10.15
outcome==1.3.0.post0
overrides==7.7.0
packaging==25.0
pandas==2.2.3
passlib==1.7.4
pathspec==0.12.1
pbs-installer==2025.4.9
pcodedmp==1.2.6
pdfkit==1.0.0
pdm==2.24.1
peewee==3.17.8
peewee-migrate==1.12.2
pefile==2023.2.7
pgvector==0.3.5
pillow==11.1.0
pkginfo==********
platformdirs==4.3.6
playwright==1.49.1
pluggy==1.5.0
poetry==2.1.3
poetry-core==2.1.3
portalocker==2.10.1
posthog==3.18.1
primp==0.14.0
prompt_toolkit==3.0.47
propcache==0.3.0
proto-plus==1.26.0
protobuf==4.25.6
psutil==7.0.0
psycopg2-binary==2.9.9
pyarrow==19.0.1
pyasn1==0.4.8
pyasn1_modules==0.4.1
pyclipper==1.3.0.post6
pycparser==2.22
pycryptodome==3.22.0
pydantic==2.10.6
pydantic-settings==2.8.1
pydantic_core==2.27.2
pydub==0.25.1
pydyf==0.11.0
pyee==12.0.0
pygame==2.6.1
Pygments==2.19.1
pyinstaller==6.5.0
pyinstaller-hooks-contrib==2024.3
PyJWT==2.10.1
pymdown-extensions==10.14.2
pymilvus==2.5.0
pymongo==3.11.4
PyMySQL==1.1.1
pypandoc==1.13
pyparsing==3.2.1
pypdf==4.3.1
pyphen==0.17.2
PyPika==0.48.9
pyproject_hooks==1.2.0
pyreadline3==3.5.4
PySocks==1.7.1
pytest==8.3.5
pytest-docker==3.1.2
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-engineio==4.11.2
python-iso639==2025.2.18
python-jose==3.4.0
python-magic==0.4.27
python-multipart==0.0.18
python-oxmsg==0.0.2
python-pptx==1.0.0
python-socketio==5.11.3
pytube==15.0.0
pytz==2024.1
pywin32==310
pywin32-ctypes==0.2.2
pyxlsb==1.0.10
PyYAML==6.0.2
qdrant-client==1.12.2
rank-bm25==0.2.2
RapidFuzz==3.12.2
rapidocr-onnxruntime==1.3.24
red-black-tree-mod==1.22
redis==5.2.1
regex==2024.11.6
requests==2.32.3
requests-file==2.1.0
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
resolvelib==1.1.0
RestrictedPython==8.0
rich==13.9.4
rsa==4.9
RTFDE==0.1.2
s3transfer==0.10.4
safetensors==0.5.3
scikit-learn==1.6.1
scipy==1.13.1
selenium==4.31.0
sentence-transformers==3.3.1
sentencepiece==0.2.0
sh==2.2.2
shapely==2.0.7
shellingham==1.5.4
simple-websocket==1.1.0
six==1.16.0
smmap==5.0.2
sniffio==1.3.1
socksio==1.0.0
sortedcontainers==2.4.0
soundfile==0.13.1
soupsieve==2.6
SQLAlchemy==2.0.32
starlette==0.45.3
sympy==1.13.1
tenacity==9.0.0
threadpoolctl==3.5.0
tiktoken==0.9.0
tinycss2==1.4.0
tinyhtml5==2.0.0
tldextract==5.1.2
tokenizers==0.21.0
tomli==2.2.1
tomlkit==0.13.2
torch==2.6.0
tqdm==4.67.1
transformers==4.49.0
trio==0.30.0
trio-websocket==0.12.2
trove-classifiers==2025.5.9.12
ttkbootstrap==1.12.0
typer==0.15.2
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2024.1
tzlocal==5.3
ujson==5.10.0
unearth==0.17.5
unstructured==0.16.17
unstructured-client==0.31.0
uritemplate==4.1.1
urllib3==1.26.20
uv==0.6.6
uvicorn==0.30.6
validators==0.34.0
vine==5.1.0
virtualenv==20.31.2
watchfiles==1.0.4
wcwidth==0.2.13
weasyprint==65.1
webdriver-manager==4.0.2
webencodings==0.5.1
websocket-client==1.8.0
websockets==15.0
Werkzeug==3.0.3
win32_setctime==1.2.0
win_unicode_console==0.5
wrapt==1.17.2
wsproto==1.2.0
xlrd==2.0.1
XlsxWriter==3.2.2
xxhash==3.5.0
yarl==1.18.3
youtube-transcript-api==0.6.3
zipp==3.18.1
zope.event==5.0
zope.interface==6.4.post2
zopfli==0.2.3.post1
zstandard==0.23.0

#!/usr/bin/env python3
"""
测试Motor 3.1.2 + PyMongo 4.2.0组合
"""
import asyncio
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_motor_3_1_2():
    """测试Motor 3.1.2兼容性"""
    try:
        # 检查版本信息
        import motor
        import pymongo
        print(f"Motor版本: {motor.version}")
        print(f"PyMongo版本: {pymongo.version}")
        
        # 导入MongoDB工具
        from utils.mongodb import get_mongodb, mongodb
        
        print("测试MongoDB连接...")
        
        # 测试获取MongoDB实例
        db_instance = await get_mongodb()
        print("✓ 成功获取MongoDB实例")
        
        # 测试客户端是否已连接
        if db_instance.client is not None:
            print("✓ MongoDB客户端已初始化")
            
            if db_instance.database is not None:
                print("✓ 成功连接到MongoDB数据库")
                
                # 测试获取集合
                try:
                    test_collection = db_instance.get_collection("test_collection")
                    print("✓ 成功获取测试集合")
                except Exception as e:
                    print(f"⚠ 获取集合时出错: {e}")
            else:
                print("⚠ 数据库连接失败")
        else:
            print("⚠ MongoDB客户端为None")
            
        # 测试断开连接
        await mongodb.disconnect()
        print("✓ 成功断开MongoDB连接")
        
        print("\n🎉 Motor 3.1.2测试通过！")
        return True
        
    except AttributeError as e:
        if "asyncio" in str(e) and "coroutine" in str(e):
            print(f"❌ asyncio.coroutine AttributeError: {e}")
            return False
        else:
            print(f"❌ 其他AttributeError: {e}")
            return False
    except Exception as e:
        error_msg = str(e)
        if "wire version" in error_msg:
            print(f"❌ MongoDB版本兼容性问题: {e}")
            return False
        else:
            print(f"⚠ 其他错误: {e}")
            return True  # 其他错误不影响asyncio.coroutine修复
    
    return True

if __name__ == "__main__":
    result = asyncio.run(test_motor_3_1_2())
    if result:
        print("\n✅ Motor 3.1.2 + PyMongo 4.2.0 组合测试成功！")
    else:
        print("\n❌ 仍存在问题")

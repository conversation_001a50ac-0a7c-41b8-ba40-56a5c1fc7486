# 导入所需的Python模块
import logging  # 用于日志记录
import logging.config  # 导入日志配置模块
import os  # 用于文件操作
from contextlib import asynccontextmanager  # 异步上下文管理器

import yaml  # 用于读取yaml配置文件
from fastapi import FastAPI, applications, Request  # FastAPI框架核心组件
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.docs import get_swagger_ui_html, get_redoc_html  # Swagger UI文档生成器
from fastapi.staticfiles import StaticFiles  # 静态文件服务

from api import wx_app,task  # 导入API路由模块
from config import (SERVER_PORT, STATIC_DIR)  # 导入配置参数
from utils import success  # 导入响应工具函数
from utils.response import ResponseModel  # 响应模型
from utils.mongodb import mongodb  # MongoDB连接

# 初始化日志记录器
log = logging.getLogger(__name__)


# 配置多进程安全的日志系统
def setup_logging():
    """配置多进程安全的日志系统"""
    # 确保日志目录存在
    os.makedirs("logs", exist_ok=True)

    # 显式加载YAML配置文件并指定UTF-8编码
    with open('logging.yaml', 'r', encoding='utf-8') as f:
        log_config = yaml.safe_load(f.read())

    # 应用日志配置
    logging.config.dictConfig(log_config)
    return log_config


# 创建 FastAPI 应用的生命周期管理器
@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    应用程序生命周期管理器
    负责在应用启动时初始化MongoDB连接，在关闭时清理资源
    """
    # 应用启动时初始化MongoDB连接
    try:
        await mongodb.connect()
        log.info("MongoDB connection initialized")
    except Exception as e:
        log.error(f"Failed to initialize MongoDB: {e}")

    yield  # 应用运行时

    # 应用关闭时清理资源
    try:
        await mongodb.disconnect()
        log.info("MongoDB connection closed")
    except Exception as e:
        log.error(f"Error closing MongoDB connection: {e}")


# 创建FastAPI应用实例
app = FastAPI(docs_url="/docs", lifespan=lifespan)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
# 注册路由模块
app.include_router(wx_app.router, prefix="/api/v1/wxapp", tags=["wxapp"])  # 微信应用路由
app.include_router(task.router, prefix="/api/v1/task", tags=["task"])  # 微信应用路由
# 挂载静态文件目录
app.mount("/static", StaticFiles(directory=STATIC_DIR), name="static")


def swagger_ui_html(*args, **kwargs):
    """
    自定义Swagger UI界面
    配置Swagger UI的静态资源路径
    """
    return get_swagger_ui_html(
        *args,
        **kwargs,
        swagger_js_url="static/swagger-ui/swagger-ui-bundle.js",  # JS文件路径
        swagger_css_url="static/swagger-ui/swagger-ui.css",  # CSS文件路径
        swagger_favicon_url="static/swagger-ui/favicon.png",  # favicon图标路径
    )


def redoc_ui_html(*args, **kwargs):
    return get_redoc_html(
        *args,
        **kwargs,
        redoc_js_url="/static/redoc/redoc.js",
        redoc_favicon_url="/static/redoc/favicon.png",
        with_google_fonts=False,
    )


# 替换默认的Swagger UI HTML生成器
applications.get_swagger_ui_html = swagger_ui_html
applications.get_redoc_html = redoc_ui_html


@app.get("/test", response_model=ResponseModel)
async def root(request: Request):
    """
    测试接口
    参数:
        userId: 用户ID
    返回:
        包含用户ID的成功响应
    """
    user_agent = request.headers.get('user-agent')
    return success(data={'user-agent': user_agent})


# 应用程序入口
if __name__ == "__main__":
    import uvicorn

    # 配置日志系统
    log_config = setup_logging()

    # 启动uvicorn服务器
    uvicorn.run(
        "main:app",  # 应用程序路径
        host="0.0.0.0",  # 监听所有网络接口
        port=int(SERVER_PORT),  # 服务端口
        timeout_keep_alive=50 * 60,  # 保持连接超时时间（50分钟）
        workers=1,  # 使用4个worker进程
        log_config=log_config  # 使用加载的配置字典而非文件路径
    )

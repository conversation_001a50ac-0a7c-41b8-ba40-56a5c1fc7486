#!/bin/bash
set -e # Exit immediately if a command exits with a non-zero status.

# FastAPI应用启动脚本 (优化版)
# 用法: ./start_optimized.sh {start|stop|restart|status} [env]
# env: 环境类型，可选值：dev(开发环境)、prd(生产环境)，默认为dev

# --- 应用配置 ---
APP_NAME="fastapi-project"
APP_DIR=$(cd "$(dirname "$0")" && pwd) # More robust way to get script directory
# APP_DIR=$(pwd) # Use this if you always run the script from the project root

# --- 虚拟环境配置 ---
VENV_DIR="$APP_DIR/.venv"
PYTHON_PATH="$VENV_DIR/bin/python3"
UVICORN_PATH="$VENV_DIR/bin/uvicorn"
# VENV_ACTIVATE_PATH="$VENV_DIR/bin/activate" # Activation not strictly needed when calling executables directly

# --- 进程与日志 ---
PID_FILE="$APP_DIR/$APP_NAME.pid"
LOG_DIR="$APP_DIR/logs"
LOG_FILE="$LOG_DIR/app_start.log" # Uvicorn's main process log
ACCESS_LOG_FILE="$LOG_DIR/access.log" # Example access log file
ERROR_LOG_FILE="$LOG_DIR/error.log"   # Example error log file (configure via log_config)

# --- Uvicorn 配置 ---
# Best practice: Read from environment variables or a config file
HOST="0.0.0.0"
PORT=${APP_PORT:-8085} # Use environment variable APP_PORT or default to 8000
WORKERS=${APP_WORKERS:-4} # Use environment variable APP_WORKERS or default to 4
APP_MODULE="main:app" # Assuming your FastAPI app instance is named 'app' in 'main.py'
LOG_CONFIG_PATH="$APP_DIR/logging.yaml" # Path to your Uvicorn logging config file

# --- 环境确定 ---
ENV_TYPE=${2:-"dev"}
echo "当前环境：$ENV_TYPE"
export ENV_TYPE=$ENV_TYPE # Export for the application if it needs it

# --- 准备工作 ---
mkdir -p "$LOG_DIR" # Ensure log directory exists
export PYTHONIOENCODING=utf-8
# export PYTHONPATH=$APP_DIR:$PYTHONPATH # Usually not needed when running modules directly

# --- 检查依赖 ---
if [ ! -f "$UVICORN_PATH" ]; then
    echo "错误：找不到 Uvicorn 可执行文件: $UVICORN_PATH"
    echo "请确保虚拟环境已创建并安装了 Uvicorn。"
    exit 1
fi
if [ ! -f "$APP_DIR/main.py" ]; then
    echo "错误：找不到应用入口文件: $APP_DIR/main.py"
    exit 1
fi
# Optional: Check for logging config file if specified
# if [ ! -f "$LOG_CONFIG_PATH" ]; then
#    echo "警告：找不到日志配置文件: $LOG_CONFIG_PATH. Uvicorn 将使用默认日志设置。"
# fi


# --- 内部函数：获取 PID ---
get_pid() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null; then
            echo "$PID"
            return 0 # PID exists and process is running
        else
            # Stale PID file
            echo "警告：发现过期的 PID 文件，进程 $PID 不存在。"
            rm -f "$PID_FILE"
            return 1 # PID not running
        fi
    else
        return 1 # No PID file
    fi
}

# --- 启动应用 ---
start() {
    echo "正在启动 $APP_NAME ($ENV_TYPE 环境)..."

    if get_pid > /dev/null; then
        local running_pid=$(get_pid)
        echo "$APP_NAME 已经在运行了，PID: $running_pid"
        return 1
    fi

    # 构建 Uvicorn 命令
    # Note: Consider using --log-config for better log management in production
    UVICORN_CMD=("$UVICORN_PATH" "$APP_MODULE" \
        --host "$HOST" \
        --port "$PORT" \
        --workers "$WORKERS" \
        --forwarded-allow-ips='*' \
        --proxy-headers)
        # Add --reload for dev environment if needed
        # if [ "$ENV_TYPE" = "dev" ]; then
        #    UVICORN_CMD+=("--reload")
        # fi
        # Add log config if file exists
         if [ -f "$LOG_CONFIG_PATH" ]; then
            UVICORN_CMD+=("--log-config" "$LOG_CONFIG_PATH")
         else
            # Example: basic logging if no config file
            UVICORN_CMD+=("--log-level" "info")
         fi
        # Add more uvicorn options as needed


    echo "执行命令: nohup ${UVICORN_CMD[@]} > \"$LOG_FILE\" 2>&1 &"
    # Start Uvicorn in the background
    # Redirect stdout/stderr of the *master* process to LOG_FILE
    nohup "${UVICORN_CMD[@]}" > "$LOG_FILE" 2>&1 &
    PID=$! # Get the PID of the nohup background process (which is the Uvicorn master process)

    # Check if process started successfully
    sleep 3
    if ps -p "$PID" > /dev/null; then
        echo "$PID" > "$PID_FILE"
        echo "$APP_NAME 已成功启动，主进程 PID: $PID"
        echo "主进程日志: $LOG_FILE"
        # Note: Worker logs might go elsewhere depending on logging config
        # echo "访问日志: $ACCESS_LOG_FILE"
        # echo "错误日志: $ERROR_LOG_FILE"
    else
        echo "$APP_NAME 启动失败，请检查日志文件: $LOG_FILE"
        # Clean up potential stale PID file if start failed immediately
        rm -f "$PID_FILE"
        return 1
    fi
}

# --- 停止应用 ---
stop() {
    echo "正在停止 $APP_NAME..."

    local pid_to_stop=$(get_pid)
    if [ -z "$pid_to_stop" ]; then
        echo "$APP_NAME 未运行"
        return 0 # Not an error if already stopped
    fi

    echo "向主进程 $pid_to_stop 发送 SIGTERM 信号..."
    # Send SIGTERM to the master process. Uvicorn should handle shutting down workers.
    if kill -15 "$pid_to_stop"; then
        # Wait for the process to terminate
        TIMEOUT=30
        while ps -p "$pid_to_stop" > /dev/null && [ $TIMEOUT -gt 0 ]; do
            echo -n "."
            sleep 1
            TIMEOUT=$((TIMEOUT-1))
        done
        echo

        if ps -p "$pid_to_stop" > /dev/null; then
            echo "进程 $pid_to_stop 未在 $TIMEOUT 秒内终止，强制终止 (SIGKILL)..."
            kill -9 "$pid_to_stop"
            sleep 1
        fi
    else
         echo "发送 SIGTERM 到进程 $pid_to_stop 失败 (可能已停止?)"
    fi

    # Final check and cleanup
    if ps -p "$pid_to_stop" > /dev/null; then
         echo "错误：无法停止进程 $pid_to_stop."
         return 1
    else
         echo "$APP_NAME 已停止"
         rm -f "$PID_FILE" # Clean up PID file after successful stop
         return 0
    fi
}

# --- 重启应用 ---
restart() {
    echo "正在重启 $APP_NAME..."
    stop
    sleep 2 # Give some time for ports to free up etc.
    start
}

# --- 检查应用状态 ---
status() {
    local current_pid=$(get_pid)
    if [ -n "$current_pid" ]; then
        echo "$APP_NAME 正在运行，主进程 PID: $current_pid，环境：$ENV_TYPE"
        # Optional: Show worker PIDs if needed (more complex)
        # echo "Worker PIDs: $(pgrep -P $current_pid)"
        return 0
    else
        # get_pid already handled stale PID file message if applicable
        echo "$APP_NAME 未运行"
        return 1
    fi
}

# --- 主逻辑 ---
case "$1" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status} [env]"
        echo "  env: 环境类型，可选值：dev(开发环境)、prd(生产环境)，默认为dev"
        exit 1
        ;;
esac

exit $? # Exit with the status code of the last command executed
#!/usr/bin/env python3
"""
简单测试脚本
"""
import sys
sys.path.append('.')

from models.api_portal import ApiPortalPublishApi, ApiPortalQueryResponse

def test_models():
    """测试数据模型"""
    print("测试数据模型...")
    
    # 测试API模型
    test_data = {
        "_id": "507f1f77bcf86cd799439011",
        "publishTime": "2025-06-01 10:30:00",
        "apiName": "测试API",
        "apiVersion": "v1.0",
        "apiDescription": "这是一个测试API",
        "publishStatus": "published",
        "publishUser": "admin"
    }
    
    api_model = ApiPortalPublishApi(**test_data)
    print(f"✅ API模型创建成功: {api_model.apiName}")
    
    # 测试响应模型
    response_data = ApiPortalQueryResponse(
        total=1,
        data=[api_model],
        limit=1000,
        skip=0
    )
    print(f"✅ 响应模型创建成功，包含 {len(response_data.data)} 条数据")
    
    return True

if __name__ == "__main__":
    try:
        test_models()
        print("\n🎉 所有测试通过!")
        print("\n📝 API接口信息:")
        print("路径: GET /api/v1/task/api_portal_publish_apis")
        print("参数:")
        print("  - start_time: 开始时间 (必需)")
        print("  - end_time: 结束时间 (必需)")
        print("  - limit: 限制数量 (可选，默认1000)")
        print("  - skip: 跳过数量 (可选，默认0)")
        print("\n示例请求:")
        print("GET /api/v1/task/api_portal_publish_apis?start_time=2025-05-30%2000:00:00&end_time=2025-06-03%2023:59:59")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        sys.exit(1)

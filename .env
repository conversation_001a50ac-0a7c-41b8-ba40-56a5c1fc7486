# 默认环境配置 (与开发环境相同)

# 数据库配置
DATABASE_URL=mysql+pymysql://iamsit:wlsp%402025@************:3306/cloud_wecom
DATABASE_SCHEMA=cloud_wecom

SERVER_PORT=8087
SERVER_HOST=*************

# 静态文件目录
STATIC_DIR=static
ESB_URL=http://***********:8080/restcloud/
ESB_USERNAME=feishu_user
ESB_PASSWORD=AFDA2E1FCB81BB05
ESB_APPKEY=67fca7fa054df711caea5275

MAIL_USERNAME = <EMAIL>
MAIL_PASSWORD = jrGHblxwnfG0IBSH
MAIL_PORT = 587
MAIL_SERVER = smtp.feishu.cn

# MongoDB配置
# 格式: mongodb://用户名:密码@主机:端口/数据库名
MONGO_URL=mongodb://mongouser:Xiaofu1125!@***********:27017
MONGO_DATABASE=RestCloud_V45PUB_Gateway
import logging
import os
from pathlib import Path

from fastapi_mail import ConnectionConfig

from env import init_env, BASE_DIR

log = logging.getLogger(__name__)

# 获取环境类型
ENV_TYPE = os.environ.get("ENV_TYPE", "prd")
# 确保环境变量被加载
init_env(ENV_TYPE)




# 获取数据库URL
DATABASE_URL = os.environ.get("DATABASE_URL", f"mysql+pymysql://iamsit:wlsp%402025@************:3306/cloud_wecom")
SERVER_NAME = os.environ.get("SERVER_NAME", "")
SERVER_PORT = os.environ.get("SERVER_PORT", "")
SERVER_HOST = os.environ.get("SERVER_HOST", "")
DATABASE_SCHEMA = os.environ.get("DATABASE_SCHEMA", None)
STATIC_DIR = Path(os.environ.get("STATIC_DIR", BASE_DIR / "static")).resolve()
ESB_URL = os.environ.get("ESB_URL", "")
ESB_USERNAME = os.environ.get("ESB_USERNAME", "")
ESB_PASSWORD = os.environ.get("ESB_PASSWORD", "")
ESB_APPKEY = os.environ.get("ESB_APPKEY", "")
DOMAIN = os.environ.get("DOMAIN", "")
MAIL_USERNAME = os.getenv("MAIL_USERNAME", "")
MAIL_PASSWORD = os.getenv("MAIL_PASSWORD", "")
MAIL_PORT = os.getenv("MAIL_PORT", "")
MAIL_SERVER = os.getenv("MAIL_SERVER", "")
MAIL_CONFIG = ConnectionConfig(
    MAIL_USERNAME=MAIL_USERNAME,
    MAIL_PASSWORD=MAIL_PASSWORD,
    MAIL_FROM=MAIL_USERNAME,
    MAIL_PORT=MAIL_PORT,
    MAIL_SERVER=MAIL_SERVER,
    MAIL_STARTTLS=True,
    MAIL_SSL_TLS=False,
    USE_CREDENTIALS=True,
    VALIDATE_CERTS=True
)

# MongoDB配置
MONGO_URL = os.environ.get("MONGO_URL", "mongodb://localhost:27017")
MONGO_DATABASE = os.environ.get("MONGO_DATABASE", "your_database_name")
# MongoDB API接口文档

## 概述

本接口用于查询MongoDB中`P_ApiPortalPublishApis`集合的数据，支持按发布时间范围进行查询。

## 接口信息

- **路径**: `GET /api/v1/task/api_portal_publish_apis`
- **描述**: 查询API门户发布API数据
- **认证**: 无需认证

## 请求参数

| 参数名 | 类型 | 必需 | 默认值 | 描述 | 示例 |
|--------|------|------|--------|------|------|
| start_time | string | 是 | - | 开始时间，格式：YYYY-MM-DD HH:MM:SS | 2025-05-30 00:00:00 |
| end_time | string | 是 | - | 结束时间，格式：YYYY-MM-DD HH:MM:SS | 2025-06-03 23:59:59 |
| limit | integer | 否 | 1000 | 限制返回数量，范围：1-10000 | 1000 |
| skip | integer | 否 | 0 | 跳过数量，用于分页 | 0 |

## MongoDB查询逻辑

接口执行的MongoDB查询等价于：

```javascript
db.getCollection("P_ApiPortalPublishApis").find({
    $and: [{
        "publishTime": {
            $gt: "2025-05-30 00:00:00"
        }
    }, {
        "publishTime": {
            $lt: "2025-06-03 23:59:59"
        }
    }]
}).sort({
    publishTime: -1
}).limit(1000).skip(0)
```

## 响应格式

### 成功响应 (200)

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "total": 150,
        "data": [
            {
                "id": "507f1f77bcf86cd799439011",
                "publishTime": "2025-06-01 10:30:00",
                "apiName": "示例API",
                "apiVersion": "v1.0",
                "apiDescription": "这是一个示例API",
                "publishStatus": "published",
                "publishUser": "admin"
            }
        ],
        "limit": 1000,
        "skip": 0
    }
}
```

### 错误响应 (500)

```json
{
    "detail": "查询失败: 具体错误信息"
}
```

## 请求示例

### 基本查询

```bash
GET /api/v1/task/api_portal_publish_apis?start_time=2025-05-30%2000:00:00&end_time=2025-06-03%2023:59:59
```

### 分页查询

```bash
GET /api/v1/task/api_portal_publish_apis?start_time=2025-05-30%2000:00:00&end_time=2025-06-03%2023:59:59&limit=50&skip=100
```

### 使用curl

```bash
curl -X GET "http://localhost:8087/api/v1/task/api_portal_publish_apis?start_time=2025-05-30%2000:00:00&end_time=2025-06-03%2023:59:59" \
     -H "accept: application/json"
```

## 配置要求

### 环境变量

在`.env`文件中配置MongoDB连接信息：

```env
# MongoDB配置
MONGO_URL=mongodb://localhost:27017
MONGO_DATABASE=your_database_name
```

### 依赖包

确保安装了以下Python包：

```
motor==3.7.1
pymongo==4.11.2
```

## 数据模型

### ApiPortalPublishApi

| 字段 | 类型 | 描述 |
|------|------|------|
| id | string | 文档ID (来自MongoDB的_id) |
| publishTime | string | 发布时间 |
| apiName | string | API名称 |
| apiVersion | string | API版本 |
| apiDescription | string | API描述 |
| publishStatus | string | 发布状态 |
| publishUser | string | 发布用户 |

## 注意事项

1. **时间格式**: 时间参数必须使用 `YYYY-MM-DD HH:MM:SS` 格式
2. **URL编码**: 在URL中使用时间参数时，需要对空格进行编码（%20）
3. **分页**: 使用 `limit` 和 `skip` 参数进行分页查询
4. **排序**: 结果按 `publishTime` 降序排列（最新的在前）
5. **连接**: 确保MongoDB服务正在运行且可访问

## 错误处理

- 如果MongoDB连接失败，将返回500错误
- 如果查询参数格式错误，将返回相应的验证错误
- 所有错误都会记录到应用日志中

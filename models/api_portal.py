from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime


class ApiPortalPublishApi(BaseModel):
    """API门户发布API数据模型"""
    id: Optional[str] = Field(None, alias="_id")
    publishTime: Optional[str] = Field(None, description="发布时间")
    apiName: Optional[str] = Field(None, description="API名称")
    apiVersion: Optional[str] = Field(None, description="API版本")
    apiDescription: Optional[str] = Field(None, description="API描述")
    publishStatus: Optional[str] = Field(None, description="发布状态")
    publishUser: Optional[str] = Field(None, description="发布用户")
    
    class Config:
        populate_by_name = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ApiPortalQueryRequest(BaseModel):
    """API门户查询请求模型"""
    start_time: str = Field(..., description="开始时间，格式：YYYY-MM-DD HH:MM:SS")
    end_time: str = Field(..., description="结束时间，格式：YYYY-MM-DD HH:MM:SS")
    limit: int = Field(1000, description="限制返回数量", ge=1, le=10000)
    skip: int = Field(0, description="跳过数量", ge=0)


class ApiPortalQueryResponse(BaseModel):
    """API门户查询响应模型"""
    total: int = Field(..., description="总数量")
    data: List[ApiPortalPublishApi] = Field(..., description="数据列表")
    limit: int = Field(..., description="限制数量")
    skip: int = Field(..., description="跳过数量")
